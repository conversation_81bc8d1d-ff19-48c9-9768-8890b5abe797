import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from '@chakra-ui/react';
import { AccumulationMoop } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getMoopPABasePath } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/moopPAConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { FC, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../productNameConstants';
import { currencyValidation, smallintValidation } from '../../../validations';

interface MoopFormModalProps {
  initialData?: Partial<AccumulationMoop>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
  productName: string;
}

export const MoopFormModal: FC<MoopFormModalProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
  productName,
}) => {
  const {
    yesNoMap,
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    integratedMap,
    embeddedMap,
    yesNoViewBenefitMap,
    sharedIndicatorMap,
    drugTypeStatusMap,
    formularyStatusMap,
    networkApplicabilityMap,
    pharmacyChannelMap,
    accumDrugListMap,
  } = usePicklistMaps();

  const basePath = getMoopPABasePath(isNewItem, itemIndex);

  const formMethods = useForm({
    defaultValues: { [basePath]: initialData },
    shouldUnregister: false,
  });

  const currentData = formMethods.watch(basePath) || initialData;

  const [doesMoopApply, setDoesMoopApply] = useState<number>(
    Number(formMethods.getValues(`${basePath}.apply_ind`) ?? 1)
  );

  useEffect(() => {
    formMethods.setValue(basePath, initialData);
    setDoesMoopApply(Number(initialData?.apply_ind) || 0);
  }, [basePath, formMethods, initialData]);

  useEffect(() => {
    const subscription = formMethods.watch((_, { name }) => {
      if (name === `${basePath}.apply_ind`) {
        const latestValue = formMethods.getValues(`${basePath}.apply_ind`);
        setDoesMoopApply(Number(latestValue));
      }
    });
    return () => subscription.unsubscribe();
  }, [formMethods, basePath]);

  const handleFormSubmit = () => {
    if (!onSave) return;
    const submitted = formMethods.getValues(basePath);
    let finalData = isNewItem
      ? { ...initialData, ...submitted }
      : { ...currentData, ...submitted };

    // If doesMoopApply === 0, set effective_date to today's date as a string
    if (doesMoopApply === 0) {
      const today = new Date();
      const yyyy = today.getFullYear();
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const dd = String(today.getDate()).padStart(2, '0');
      const formattedToday = `${yyyy}-${mm}-${dd}`;
      finalData = {
        ...finalData,
        effective_date: formattedToday,
      };
    }
    onSave(finalData);
  };

  const formConfig = useMemo(
    () => ({
      subCategories: [
        defineSubCategory('', '', [
          defineInlineFieldGroup([
            ...(productNames.CMK_360 === productName ||
            productNames.ESI_360 === productName ||
            productNames.OPT_360 === productName ||
            productNames.IRX_360 === productName
              ? [
                  defineFormField(
                    'Does Maximum Out of Pocket Apply?',
                    'dropdownSelect',
                    `${basePath}.apply_ind`,
                    currentData?.apply_ind,
                    {
                      isRequired: true,
                      optionsMap: yesNoMap,
                      validations: smallintValidation,
                    }
                  ),
                ]
              : []),
            ...(doesMoopApply === 1 &&
            (productNames.CMK_360 === productName ||
              productNames.ESI_360 === productName ||
              productNames.OPT_360 === productName ||
              productNames.IRX_360 === productName)
              ? [
                  defineFormField(
                    'Accums Tier Name',
                    'input',
                    `${basePath}.accums_tier_name`,
                    currentData?.accums_tier_name,
                    {
                      placeholder: 'Enter Accums Tier Name',
                      validations: z
                        .string()
                        .max(
                          255,
                          'Accums Tier Name must be less than 255 characters'
                        )
                        .optional()
                        .nullable(),
                    }
                  ),
                ]
              : []),
          ]),
          ...(doesMoopApply === 1
            ? [
                ...(productName === productNames.CMK_360 ||
                productName === productNames.ESI_360 ||
                productName === productNames.OPT_360 ||
                productName === productNames.IRX_360
                  ? [
                      defineInlineFieldGroup([
                        defineFormField(
                          'Accums Tier Effective Date',
                          'datepicker',
                          `${basePath}.effective_date`,
                          currentData?.effective_date,
                          {
                            validations: z.date(),
                          }
                        ),
                        defineFormField(
                          'Accums Tier End Date',
                          'datepicker',
                          `${basePath}.expiration_date`,
                          currentData?.expiration_date,
                          {
                            validations: z.date().optional().nullable(),
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Accums Tier PBC Order',
                          'input',
                          `${basePath}.pbc_order`,
                          currentData?.pbc_order,
                          {
                            isRequired: false,
                            placeholder: 'Enter Accums Tier PBC Order',
                            validations: z.number().int().optional().nullable(),
                          }
                        ),
                        defineFormField(
                          'Maximum Out of Pocket Accumulation Period',
                          'dropdownSelect',
                          `${basePath}.accum_period_ind`,
                          currentData?.accum_period_ind,
                          {
                            infoText:
                              'Whether the accums period is calendar year (Jan-Dec) or other (e.g. Jul-Jun).',
                            optionsMap: esiAccumPeriodMap,
                            validations: z.string().optional(),
                          }
                        ),
                      ]),
                    ]
                  : []),
                defineInlineFieldGroup([
                  ...(productName === productNames.CMK_360 ||
                  productName === productNames.ESI_360 ||
                  productName === productNames.OPT_360 ||
                  productName === productNames.IRX_360
                    ? [
                        defineFormField(
                          'Specify Maximum Out of Pocket Accumulation Period',
                          'dropdownSelect',
                          `${basePath}.specify_accum_period_ind`,
                          currentData?.specify_accum_period_ind,
                          {
                            infoText:
                              'The first through last months that Rx spend counts towards the MOOP (e.g. Apr-Mar).',
                            optionsMap: benefitPeriodsMap,
                          }
                        ),
                      ]
                    : []),
                  ...(productName === productNames.ESI_360
                    ? [
                        defineFormField(
                          'Benefit Period Length',
                          'dropdownSelect',
                          `${basePath}.benefit_period_length_ind`,
                          currentData?.benefit_period_length_ind,
                          {
                            infoText:
                              "Whether the accumulator resets every year, lasts for the patient's lifetime, or other (specify).",
                            optionsMap: benefitPeriodLengthMap,
                          }
                        ),
                      ]
                    : []),
                ]),
                defineInlineFieldGroup([
                  ...(productName === productNames.ESI_360
                    ? [
                        defineFormField(
                          'Benefit Period Length - Other',
                          'input',
                          `${basePath}.benefit_period_length_other`,
                          currentData?.benefit_period_length_other,
                          {
                            infoText:
                              'Used to specify the Benefit Period, if not calendar or lifetime.',
                            placeholder: 'Enter Benefit Period Length - Other',
                            validations: z.number().int().optional().nullable(),
                          }
                        ),
                      ]
                    : []),
                  ...(productName === productNames.CMK_360 ||
                  productName === productNames.ESI_360 ||
                  productName === productNames.OPT_360 ||
                  productName === productNames.IRX_360
                    ? [
                        defineFormField(
                          'Do Priming Balances Apply?',
                          'dropdownSelect',
                          `${basePath}.priming_balances_ind`,
                          currentData?.priming_balances_ind,
                          {
                            infoText:
                              'Carries existing MOOP balances forward when starting a new plan in the middle of current benefit period.',
                            optionsMap: yesNoMap,
                          }
                        ),
                      ]
                    : []),
                ]),
                ...(productName === productNames.CMK_360 ||
                productName === productNames.ESI_360 ||
                productName === productNames.OPT_360 ||
                productName === productNames.IRX_360
                  ? [
                      defineInlineFieldGroup([
                        defineFormField(
                          'Carryover Phase',
                          'dropdownSelect',
                          `${basePath}.carryover_phase_ind`,
                          currentData?.carryover_phase_ind,
                          {
                            infoText:
                              'Denotes whether any part of the benefit carries over from one benefit/accum year to the next.',
                            optionsMap: carryoverPhaseMap,
                          }
                        ),
                        defineFormField(
                          'Describe Carryover Phase',
                          'input',
                          `${basePath}.describe_carryover_phase`,
                          currentData?.describe_carryover_phase,
                          {
                            infoText:
                              'Provides details of the carryover phase.',
                            placeholder: 'Describe Carryover Phase',
                            validations: z
                              .string()
                              .max(
                                2000,
                                'Value must be less than 2000 characters'
                              )
                              .optional()
                              .nullable(),
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Maximum Out of Pocket Integrated?',
                          'dropdownSelect',
                          `${basePath}.integrated_ind`,
                          currentData?.integrated_ind,
                          {
                            infoText:
                              'Whether MOOP is Rx-only (separate) or combined with medical (integrated).',
                            optionsMap: integratedMap,
                          }
                        ),
                        defineFormField(
                          'Maximum Out of Pocket Embedded?',
                          'dropdownSelect',
                          `${basePath}.embedded_ind`,
                          currentData?.embedded_ind,
                          {
                            infoText:
                              'Embedded Accums = Each member must meet the individual MOOP.',
                            optionsMap: embeddedMap,
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Individual Maximum Out of Pocket Amount',
                          'input',
                          `${basePath}.individual_plan_amount`,
                          currentData?.individual_plan_amount,
                          {
                            placeholder:
                              'Enter Individual Maximum Out of Pocket Amount',
                            validations: currencyValidation.optional(),
                          }
                        ),
                        defineFormField(
                          'Family Maximum Out of Pocket Amount',
                          'input',
                          `${basePath}.family_plan_amount`,
                          currentData?.family_plan_amount,
                          {
                            placeholder:
                              'Enter Family Maximum Out of Pocket Amount',
                            validations: currencyValidation.optional(),
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Employee +1 Dep Maximum Out of Pocket Amount',
                          'input',
                          `${basePath}.employee_1_dep_amount`,
                          currentData?.employee_1_dep_amount,
                          {
                            placeholder:
                              'Enter Employee +1 Dep Maximum Out of Pocket Amount',
                            validations: currencyValidation.optional(),
                          }
                        ),
                        defineFormField(
                          'Individual Maximum Out of Pocket within Family Amount',
                          'input',
                          `${basePath}.individual_within_family_amount`,
                          currentData?.individual_within_family_amount,
                          {
                            infoText:
                              'For non-embedded plans. Some plans only allow the individual to hit up to a certain amount of the family MOOP.',
                            placeholder:
                              'Enter Individual Maximum Out of Pocket within Family Amount',
                            validations: currencyValidation.optional(),
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Maximum Out of Pocket Applies to Retail, Mail & Paper',
                          'dropdownSelect',
                          `${basePath}.apply_retail_mail_paper_ind`,
                          currentData?.apply_retail_mail_paper_ind,
                          {
                            infoText:
                              'Any claim from retail, mail or submitted via paper applies to MOOP.',
                            optionsMap: yesNoMap,
                          }
                        ),
                        defineFormField(
                          'Penalties Apply To Maximum Out of Pocket',
                          'dropdownSelect',
                          `${basePath}.penalties_apply_ind`,
                          currentData?.penalties_apply_ind,
                          {
                            infoText:
                              'Indicates whether DAW penalties count towards the MOOP.',
                            optionsMap: yesNoViewBenefitMap,
                          }
                        ),
                      ]),
                    ]
                  : []),
                defineInlineFieldGroup([
                  ...(productName === productNames.CMK_360 ||
                  productName === productNames.ESI_360 ||
                  productName === productNames.OPT_360 ||
                  productName === productNames.IRX_360
                    ? [
                        defineFormField(
                          'Penalties Apply After Maximum Out of Pocket',
                          'dropdownSelect',
                          `${basePath}.penalties_apply_after_ind`,
                          currentData?.penalties_apply_after_ind,
                          {
                            infoText:
                              'Indicates whether DAW penalties are incurred after the MOOP is met.',
                            optionsMap: yesNoViewBenefitMap,
                          }
                        ),
                      ]
                    : []),
                  ...(productName === productNames.ESI_360
                    ? [
                        defineFormField(
                          'Shared Indicator',
                          'dropdownSelect',
                          `${basePath}.shared_ind`,
                          currentData?.shared_ind,
                          {
                            optionsMap: sharedIndicatorMap,
                          }
                        ),
                      ]
                    : []),
                ]),
                ...(productName === productNames.ESI_360
                  ? [
                      defineInlineFieldGroup([
                        defineFormField(
                          'Drug Type Status',
                          'dropdownSelect',
                          `${basePath}.drug_type_status_ind`,
                          currentData?.drug_type_status_ind,
                          {
                            optionsMap: drugTypeStatusMap,
                          }
                        ),
                        defineFormField(
                          'Formulary Status',
                          'dropdownSelect',
                          `${basePath}.formulary_status_ind`,
                          currentData?.formulary_status_ind,
                          {
                            optionsMap: formularyStatusMap,
                          }
                        ),
                      ]),
                    ]
                  : []),
                ...(productName === productNames.CMK_360 ||
                productName === productNames.ESI_360 ||
                productName === productNames.OPT_360 ||
                productName === productNames.IRX_360
                  ? [
                      defineInlineFieldGroup([
                        defineFormField(
                          'Specialty Maximum Out of Pocket Amount',
                          'input',
                          `${basePath}.specialty_moop_amount`,
                          currentData?.specialty_moop_amount,
                          {
                            placeholder:
                              'Enter Specialty Maximum Out of Pocket Amount',
                            validations: currencyValidation.optional(),
                          }
                        ),
                      ]),
                    ]
                  : []),

                defineInlineFieldGroup([
                  ...(productName === productNames.CMK_360 ||
                  productName === productNames.ESI_360 ||
                  productName === productNames.OPT_360 ||
                  productName === productNames.IRX_360
                    ? [
                        defineFormField(
                          'Network Status',
                          'dropdownSelect',
                          `${basePath}.network_status_ind`,
                          currentData?.network_status_ind,
                          {
                            optionsMap: networkApplicabilityMap,
                          }
                        ),
                      ]
                    : []),
                  ...(productName === productNames.ESI_360
                    ? [
                        defineFormField(
                          'Pharmacy Channel',
                          'dropdownSelect',
                          `${basePath}.pharmacy_channel_ind`,
                          currentData?.pharmacy_channel_ind,
                          {
                            optionsMap: pharmacyChannelMap,
                          }
                        ),
                      ]
                    : []),
                ]),
                ...(productName === productNames.ESI_360
                  ? [
                      defineInlineFieldGroup([
                        defineFormField(
                          'Include Drug List',
                          'dropdownSelect',
                          `${basePath}.include_drug_list_ind`,
                          currentData?.include_drug_list_ind,
                          {
                            optionsMap: accumDrugListMap,
                          }
                        ),
                        defineFormField(
                          'Exclude Drug List',
                          'dropdownSelect',
                          `${basePath}.exclude_drug_list_ind`,
                          currentData?.exclude_drug_list_ind,
                          {
                            optionsMap: accumDrugListMap,
                          }
                        ),
                      ]),
                      defineInlineFieldGroup([
                        defineFormField(
                          'Notes',
                          'textarea',
                          `${basePath}.notes`,
                          currentData?.notes,
                          {
                            placeholder: 'Enter Maximum Out of Pocket Notes',
                            validations: z
                              .string()
                              .max(
                                2000,
                                'Value must be less than 2000 characters'
                              )
                              .optional()
                              .nullable(),
                            rows: 5,
                            customProps: {
                              minHeight: '120px',
                              overflow: 'hidden',
                            },
                          }
                        ),
                      ]),
                    ]
                  : []),
              ]
            : []),
        ]),
      ],
    }),
    [
      productName,
      basePath,
      currentData,
      yesNoMap,
      doesMoopApply,
      esiAccumPeriodMap,
      benefitPeriodsMap,
      benefitPeriodLengthMap,
      carryoverPhaseMap,
      integratedMap,
      embeddedMap,
      yesNoViewBenefitMap,
      sharedIndicatorMap,
      drugTypeStatusMap,
      formularyStatusMap,
      networkApplicabilityMap,
      pharmacyChannelMap,
      accumDrugListMap,
    ]
  );

  return (
    <>
      <ModalBody>
        <GenericForm
          formMethods={formMethods}
          formName="Maximum Out of Pocket"
          formDescription="Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information."
          subCategories={formConfig.subCategories || []}
          showButtons={false}
          isInModal
        />
      </ModalBody>
      <ModalFooter>
        <Button variant="outline" mr={3} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          colorScheme="green"
          onClick={formMethods.handleSubmit(handleFormSubmit)}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
