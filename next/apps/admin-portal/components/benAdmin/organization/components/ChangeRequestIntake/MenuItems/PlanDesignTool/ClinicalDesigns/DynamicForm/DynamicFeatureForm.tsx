// import {
//   CLINICAL_DESIGN_MODE,
//   useSaveChangeRequestHandler,
// } from 'apps/admin-portal/components/benAdmin';
import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { HelpCenterContextParam } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { createMultiPlanSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { usePharmacyNetworkFormFields } from '../../ProductsAndServices/CoreProducts/PharmacyNetwork/usePharmacyNetworkFormFields';
import { useDynamicFeatureFields } from './useDynamicFeatureFields';

interface DynamicFeatureFormProps {
  feature: Feature;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  nextItemId?: string; // ID of the next item to navigate to
  previousItemId?: string;
  refetch: () => Promise<any>;
  isLoading: boolean;
}

/**
 * A dynamic form component that renders a form for any feature
 * Uses optimized hooks and utilities for form generation and submission
 */
const DynamicFeatureForm: React.FC<DynamicFeatureFormProps> = ({
  feature,
  formMethods,
  onUpdateActiveItem,
  nextItemId,
  previousItemId,
  refetch,
  isLoading,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  //   // Get the base save handler from the form system
  //   const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  //   const applyChangesToAllPlans = !getValues(`toggles.${CLINICAL_DESIGN_MODE}`);

  // Handle save and exit action
  //   const handleSaveAndExit = useCallback(() => {
  //     const currentValues = getValues();

  //     if (!applyChangesToAllPlans) {
  //       // When toggle is active (alternate view), don't apply to all plans
  //       baseSaveHandler(currentValues);
  //     } else {
  //       // When toggle is not active (default view), apply to all plans
  //       const submitHandler = createMultiPlanSubmitHandler(
  //         baseSaveHandler,
  //         feature
  //       );
  //       submitHandler(currentValues);
  //     }
  //   }, [getValues, baseSaveHandler, feature, applyChangesToAllPlans]);

  const contextInd =
    feature?.product_class_feature_id || feature?.product_feature_id;

  // Determine which parameter name to use based on which feature ID exists
  const contextParamName: HelpCenterContextParam =
    feature?.product_class_feature_id
      ? HelpCenterContextParam.PRODUCT_CLASS_FEATURE_ID
      : HelpCenterContextParam.PRODUCT_FEATURE_ID;

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    contextInd,
    nextItemId ? (nextItemId as string) : 'CLINICAL_DESIGN_REVIEW',
    contextParamName
  );

  // Call both hooks unconditionally
  const pharmacyNetworkFields = usePharmacyNetworkFormFields(
    feature,
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );
  const dynamicFields = useDynamicFeatureFields(
    feature,
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );
  const isPharmacyNetwork = /^Pharmacy Network$|^.*Pharmacy Network$/.test(
    feature.label || feature.name || ''
  );

  // Get form subcategories using our optimized hook
  const { subCategories } = isPharmacyNetwork
    ? pharmacyNetworkFields
    : dynamicFields;

  const handleBack = useCallback(() => {
    if (onUpdateActiveItem && previousItemId) {
      onUpdateActiveItem(previousItemId);
    }
  }, [onUpdateActiveItem, previousItemId]);

  return (
    <GenericForm
      formMethods={formMethods}
      formName={feature.label || feature.name || ''}
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      {...(previousItemId && { onBack: handleBack })} // Disable Back for Clinical Design Note
      //   onSaveExit={handleSaveAndExit}
      isProcessing={isLoading}
    />
  );
};

export default React.memo(DynamicFeatureForm);
